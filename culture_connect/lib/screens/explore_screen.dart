import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/widgets/experience_card.dart';
import 'package:culture_connect/screens/map_view_screen.dart';
import 'package:culture_connect/screens/experience_details_screen.dart';
import 'package:culture_connect/providers/experience_provider.dart';
import 'package:culture_connect/screens/ar_explore_screen.dart';
import 'package:culture_connect/screens/ar_tutorial_screen.dart';
import 'package:culture_connect/widgets/filter_dialog.dart';
import 'package:culture_connect/widgets/saved_experiences_section.dart';
import 'package:culture_connect/widgets/recently_viewed_section.dart';
import 'package:culture_connect/widgets/popular_experiences_section.dart';
import 'package:culture_connect/widgets/category_experiences_section.dart';
import 'package:culture_connect/models/filter_options.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/widgets/interactive/enhanced_gesture_detector.dart';
import 'package:culture_connect/widgets/interactive/enhanced_loading_states.dart';
import 'package:culture_connect/widgets/interactive/enhanced_error_states.dart'
    as enhanced_error;
import 'package:connectivity_plus/connectivity_plus.dart';

class ExploreScreen extends ConsumerStatefulWidget {
  const ExploreScreen({super.key});

  @override
  ConsumerState<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends ConsumerState<ExploreScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  final List<String> _categories = [
    'All',
    'Cultural Tours',
    'Cooking Classes',
    'Art & Craft',
    'Music & Dance',
    'Language Exchange',
    'Local Events',
    'Nature & Wildlife',
    'Food & Drink',
  ];

  bool _isSearching = false;
  bool _isLoadingMore = false;
  bool _hasMoreItems = true;
  int _currentPage = 1;
  final int _itemsPerPage = 10;
  bool _isOffline = false;
  StreamSubscription? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _checkConnectivity();
    _setupConnectivityListener();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  Future<void> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      setState(() {
        _isOffline = connectivityResult == ConnectivityResult.none;
      });
    } catch (e, stackTrace) {
      final errorHandlingService = ref.read(errorHandlingServiceProvider);
      await errorHandlingService.handleError(
        error: e,
        context: 'ExploreScreen._checkConnectivity',
        stackTrace: stackTrace,
        type: ErrorType.network,
        severity: ErrorSeverity.low,
      );
    }
  }

  void _setupConnectivityListener() {
    _connectivitySubscription =
        Connectivity().onConnectivityChanged.listen((result) {
      setState(() {
        _isOffline = result == ConnectivityResult.none;
      });

      if (!_isOffline) {
        // Refresh data when coming back online
        ref.read(experiencesProvider.notifier).refreshExperiences();
      }
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore &&
        _hasMoreItems) {
      _loadMoreItems();
    }
  }

  Future<void> _loadMoreItems() async {
    if (_isOffline) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Simulate loading more items
      await Future.delayed(const Duration(seconds: 1));

      // In a real app, you would fetch the next page of items from the API
      // For now, we'll just simulate it by checking if we've reached the end
      final filteredExperiences = ref.read(filteredExperiencesProvider);
      final totalPages = (filteredExperiences.length / _itemsPerPage).ceil();

      if (_currentPage < totalPages) {
        _currentPage++;
      } else {
        _hasMoreItems = false;
      }
    } catch (e, stackTrace) {
      final errorHandlingService = ref.read(errorHandlingServiceProvider);
      await errorHandlingService.handleError(
        error: e,
        context: 'ExploreScreen._loadMoreItems',
        stackTrace: stackTrace,
        type: ErrorType.network,
        severity: ErrorSeverity.low,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => const FilterDialog(),
    );
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final experiencesAsync = ref.watch(experiencesProvider);
    final filterOptions = ref.watch(filterOptionsProvider);
    final filteredExperiences = ref.watch(filteredExperiencesProvider);

    // Calculate how many items to show based on current page
    final itemsToShow =
        filteredExperiences.take(_currentPage * _itemsPerPage).toList();

    return Scaffold(
      body: EnhancedRefreshIndicator(
        onRefresh: () async {
          await ref.read(experiencesProvider.notifier).refreshExperiences();
          // Reset pagination
          setState(() {
            _currentPage = 1;
            _hasMoreItems = true;
          });
        },
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverAppBar(
              floating: true,
              pinned: true,
              title: const Text('Explore'),
              actions: [
                IconButton(
                  icon: Icon(_isSearching ? Icons.close : Icons.search),
                  onPressed: _toggleSearch,
                ),
                IconButton(
                  icon: Badge(
                    isLabelVisible: filterOptions.isAnyFilterApplied,
                    child: const Icon(Icons.filter_list),
                  ),
                  onPressed: _showFilterDialog,
                ),
              ],
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_isSearching) ...[
                      // Enhanced Search Bar with AirBnB-inspired design
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(13),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                              spreadRadius: 0,
                            ),
                            BoxShadow(
                              color: Colors.black.withAlpha(8),
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                              spreadRadius: 0,
                            ),
                          ],
                        ),
                        child: TextField(
                          controller: _searchController,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Where would you like to explore?',
                            hintStyle: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              color: Colors.grey[600],
                            ),
                            prefixIcon: Container(
                              margin: const EdgeInsets.all(12),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: theme.primaryColor.withAlpha(26),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.search,
                                size: 20,
                                color: theme.primaryColor,
                              ),
                            ),
                            suffixIcon: Container(
                              margin: const EdgeInsets.all(8),
                              child: Material(
                                color: theme.primaryColor,
                                borderRadius: BorderRadius.circular(16),
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(16),
                                  onTap: () {
                                    // TODO: Implement voice search
                                  },
                                  child: Container(
                                    width: 32,
                                    height: 32,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: const Icon(
                                      Icons.mic,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16,
                            ),
                          ),
                          onChanged: (value) {
                            setState(() {});
                          },
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],

                    // Enhanced Categories with AirBnB-inspired design
                    SizedBox(
                      height: 48,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          final isSelected = category == filterOptions.category;
                          return Padding(
                            padding: const EdgeInsets.only(right: 12),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(24),
                                onTap: () {
                                  // Update category filter
                                  ref
                                          .read(filterOptionsProvider.notifier)
                                          .state =
                                      filterOptions.copyWith(
                                          category: category);
                                },
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? theme.primaryColor
                                        : Colors.white,
                                    borderRadius: BorderRadius.circular(24),
                                    border: Border.all(
                                      color: isSelected
                                          ? theme.primaryColor
                                          : Colors.grey.withAlpha(51),
                                      width: 1,
                                    ),
                                    boxShadow: isSelected
                                        ? [
                                            BoxShadow(
                                              color: theme.primaryColor
                                                  .withAlpha(51),
                                              blurRadius: 6,
                                              offset: const Offset(0, 2),
                                            ),
                                          ]
                                        : [
                                            BoxShadow(
                                              color: Colors.black.withAlpha(8),
                                              blurRadius: 2,
                                              offset: const Offset(0, 1),
                                            ),
                                          ],
                                  ),
                                  child: Text(
                                    category,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: isSelected
                                          ? FontWeight.w600
                                          : FontWeight.w500,
                                      color: isSelected
                                          ? Colors.white
                                          : theme.textTheme.bodyMedium?.color,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),

            // Offline banner
            if (_isOffline)
              SliverToBoxAdapter(
                child: Container(
                  color: Colors.orange,
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: Row(
                    children: [
                      const Icon(Icons.wifi_off, color: Colors.white),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'You are offline. Some features may be limited.',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                      TextButton(
                        onPressed: () async {
                          await _checkConnectivity();
                          if (!_isOffline) {
                            ref
                                .read(experiencesProvider.notifier)
                                .refreshExperiences();
                          }
                        },
                        child: const Text(
                          'RETRY',
                          style: TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Experience List
            experiencesAsync.when(
              data: (experiences) {
                // Filter by search query
                var displayedExperiences = itemsToShow;
                if (_searchController.text.isNotEmpty) {
                  displayedExperiences = ref
                      .read(experiencesProvider.notifier)
                      .searchExperiences(_searchController.text);
                }

                if (displayedExperiences.isEmpty) {
                  return SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.search_off,
                            size: 64,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No experiences found',
                            style: theme.textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Try adjusting your filters or search terms',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              ref.read(filterOptionsProvider.notifier).state =
                                  const FilterOptions();
                              _searchController.clear();
                              setState(() {});
                            },
                            child: const Text('Clear Filters'),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return _isSearching || filterOptions.isAnyFilterApplied
                    ? _buildFilteredExperiencesGrid(displayedExperiences)
                    : _buildHomeExploreView();
              },
              loading: () => SliverFillRemaining(
                child: EnhancedListSkeletonLoader(
                  itemCount: 5,
                  itemBuilder: (index) =>
                      const EnhancedExperienceSkeletonCard(),
                ),
              ),
              error: (error, stackTrace) => SliverFillRemaining(
                child: enhanced_error.EnhancedServerError(
                  onRetry: () {
                    ref.read(experiencesProvider.notifier).refreshExperiences();
                  },
                  onGoBack: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: 'ar_button',
            onPressed: () {
              // Show a dialog to choose between tutorial and direct AR access
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('AR Experience'),
                  content: const Text(
                      'Would you like to view a tutorial on how to use the AR features, or go directly to the AR experience?'),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context); // Close dialog
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ARTutorialScreen(),
                          ),
                        );
                      },
                      child: const Text('View Tutorial'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context); // Close dialog
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ARExploreScreen(),
                          ),
                        );
                      },
                      child: const Text('Skip Tutorial'),
                    ),
                  ],
                ),
              );
            },
            backgroundColor: Theme.of(context).colorScheme.secondary,
            child: const Icon(Icons.view_in_ar),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: 'map_button',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MapViewScreen(),
                ),
              );
            },
            child: const Icon(Icons.map),
          ),
        ],
      ),
    );
  }

  Widget _buildFilteredExperiencesGrid(List<dynamic> experiences) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == experiences.length) {
            return _isLoadingMore
                ? const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: EnhancedExperienceSkeletonCard(),
                  )
                : const SizedBox.shrink();
          }

          final experience = experiences[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: SwipeableCard(
              onSwipeRight: () {
                // Add to favorites
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Added to favorites!')),
                );
              },
              onSwipeLeft: () {
                // Share experience
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Sharing experience...')),
                );
              },
              rightActionIcon: Icons.favorite,
              leftActionIcon: Icons.share,
              rightActionColor: Colors.red,
              leftActionColor: Colors.blue,
              child: ExperienceCard(
                title: experience.title,
                imageUrl: experience.imageUrl,
                rating: experience.rating,
                reviewCount: experience.reviewCount,
                price: experience.price.toString(),
                category: experience.category,
                location: experience.location,
                onTap: () {
                  ref
                      .read(experiencesProvider.notifier)
                      .markAsViewed(experience.id);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ExperienceDetailsScreen(
                        experience: experience,
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
        childCount: experiences.length + (_isLoadingMore ? 1 : 0),
      ),
    );
  }

  Widget _buildHomeExploreView() {
    return SliverList(
      delegate: SliverChildListDelegate([
        // Recently viewed section
        const RecentlyViewedSection(),

        // Saved experiences section
        const SavedExperiencesSection(),

        // Popular experiences section
        const PopularExperiencesSection(),

        // Category-specific sections
        const CategoryExperiencesSection(
          category: 'Cultural Tours',
        ),

        const CategoryExperiencesSection(
          category: 'Cooking Classes',
        ),

        const CategoryExperiencesSection(
          category: 'Nature & Wildlife',
        ),

        const CategoryExperiencesSection(
          category: 'Music & Dance',
        ),

        // Loading indicator at the bottom
        if (_isLoadingMore)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          ),
      ]),
    );
  }
}
