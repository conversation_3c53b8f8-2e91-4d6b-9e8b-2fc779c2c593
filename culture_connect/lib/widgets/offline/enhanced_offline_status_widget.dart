import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/providers/offline_mode_provider.dart';
import 'package:culture_connect/providers/connectivity_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/utils/app_colors.dart';

/// Enhanced offline status widget with detailed sync information
class EnhancedOfflineStatusWidget extends ConsumerStatefulWidget {
  final bool showDetails;
  final bool showSyncProgress;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;

  const EnhancedOfflineStatusWidget({
    super.key,
    this.showDetails = true,
    this.showSyncProgress = true,
    this.onTap,
    this.padding,
  });

  @override
  ConsumerState<EnhancedOfflineStatusWidget> createState() =>
      _EnhancedOfflineStatusWidgetState();
}

class _EnhancedOfflineStatusWidgetState
    extends ConsumerState<EnhancedOfflineStatusWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _syncController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _syncAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _syncController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _syncAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _syncController,
      curve: Curves.easeInOut,
    ));
    
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _syncController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isOnline = ref.watch(connectivityProvider);
    final offlineMode = ref.watch(offlineModeProvider);
    final syncStatus = offlineMode.syncStatus;

    // Control animations based on sync status
    if (syncStatus == SyncStatus.syncing) {
      if (!_syncController.isAnimating) {
        _syncController.repeat();
      }
    } else {
      _syncController.stop();
    }

    return GestureDetector(
      onTap: widget.onTap ?? () => _showOfflineStatusDialog(context),
      child: Container(
        padding: widget.padding ?? const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: _getStatusColor(isOnline, syncStatus).withAlpha(26),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: _getStatusColor(isOnline, syncStatus).withAlpha(77),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatusIcon(isOnline, syncStatus),
            if (widget.showDetails) ...[
              const SizedBox(width: 8),
              _buildStatusText(isOnline, syncStatus),
            ],
            if (widget.showSyncProgress && syncStatus == SyncStatus.syncing) ...[
              const SizedBox(width: 8),
              _buildSyncProgress(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon(bool isOnline, SyncStatus syncStatus) {
    IconData icon;
    Color color;

    if (!isOnline) {
      icon = FontAwesomeIcons.wifiSlash;
      color = Colors.red;
    } else {
      switch (syncStatus) {
        case SyncStatus.syncing:
          icon = FontAwesomeIcons.arrowsRotate;
          color = Colors.blue;
          break;
        case SyncStatus.synced:
          icon = FontAwesomeIcons.check;
          color = Colors.green;
          break;
        case SyncStatus.failed:
          icon = FontAwesomeIcons.triangleExclamation;
          color = Colors.orange;
          break;
        case SyncStatus.pending:
          icon = FontAwesomeIcons.clock;
          color = Colors.amber;
          break;
        case SyncStatus.offline:
          icon = FontAwesomeIcons.download;
          color = Colors.grey;
          break;
      }
    }

    Widget iconWidget = FaIcon(
      icon,
      size: 16,
      color: color,
    );

    // Add animations for specific states
    if (!isOnline) {
      iconWidget = AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: child,
          );
        },
        child: iconWidget,
      );
    } else if (syncStatus == SyncStatus.syncing) {
      iconWidget = AnimatedBuilder(
        animation: _syncAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _syncAnimation.value * 2 * 3.14159,
            child: child,
          );
        },
        child: iconWidget,
      );
    }

    return iconWidget;
  }

  Widget _buildStatusText(bool isOnline, SyncStatus syncStatus) {
    String text;
    Color color;

    if (!isOnline) {
      text = 'Offline';
      color = Colors.red;
    } else {
      switch (syncStatus) {
        case SyncStatus.syncing:
          text = 'Syncing...';
          color = Colors.blue;
          break;
        case SyncStatus.synced:
          text = 'Synced';
          color = Colors.green;
          break;
        case SyncStatus.failed:
          text = 'Sync Failed';
          color = Colors.orange;
          break;
        case SyncStatus.pending:
          text = 'Sync Pending';
          color = Colors.amber;
          break;
        case SyncStatus.offline:
          text = 'Offline Mode';
          color = Colors.grey;
          break;
      }
    }

    return Text(
      text,
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: color,
      ),
    );
  }

  Widget _buildSyncProgress() {
    return SizedBox(
      width: 16,
      height: 16,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
      ),
    );
  }

  Color _getStatusColor(bool isOnline, SyncStatus syncStatus) {
    if (!isOnline) return Colors.red;

    switch (syncStatus) {
      case SyncStatus.syncing:
        return Colors.blue;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.failed:
        return Colors.orange;
      case SyncStatus.pending:
        return Colors.amber;
      case SyncStatus.offline:
        return Colors.grey;
    }
  }

  void _showOfflineStatusDialog(BuildContext context) {
    final isOnline = ref.read(connectivityProvider);
    final offlineMode = ref.read(offlineModeProvider);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            _buildStatusIcon(isOnline, offlineMode.syncStatus),
            const SizedBox(width: 12),
            Text(
              isOnline ? 'Online Status' : 'Offline Status',
              style: const TextStyle(fontSize: 18),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusRow('Connection', isOnline ? 'Online' : 'Offline'),
            const SizedBox(height: 8),
            _buildStatusRow('Sync Status', _getSyncStatusText(offlineMode.syncStatus)),
            const SizedBox(height: 8),
            _buildStatusRow('Cached Items', '${offlineMode.cachedContentCount}'),
            const SizedBox(height: 8),
            _buildStatusRow('Last Sync', _getLastSyncText(offlineMode.lastSyncTime)),
            if (offlineMode.syncStatus == SyncStatus.failed) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withAlpha(77)),
                ),
                child: Row(
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.triangleExclamation,
                      size: 16,
                      color: Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Sync failed. Check your connection and try again.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          if (isOnline && offlineMode.syncStatus != SyncStatus.syncing)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref.read(offlineModeProvider.notifier).syncOfflineContent();
              },
              child: const Text('Sync Now'),
            ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushNamed(context, '/offline/dashboard');
            },
            child: const Text('Manage Offline'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _getSyncStatusText(SyncStatus status) {
    switch (status) {
      case SyncStatus.syncing:
        return 'Syncing...';
      case SyncStatus.synced:
        return 'Up to date';
      case SyncStatus.failed:
        return 'Failed';
      case SyncStatus.pending:
        return 'Pending';
      case SyncStatus.offline:
        return 'Offline mode';
    }
  }

  String _getLastSyncText(DateTime? lastSync) {
    if (lastSync == null) return 'Never';
    
    final now = DateTime.now();
    final difference = now.difference(lastSync);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

/// Compact offline status indicator for app bars
class CompactOfflineStatusIndicator extends ConsumerWidget {
  final VoidCallback? onTap;

  const CompactOfflineStatusIndicator({
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isOnline = ref.watch(connectivityProvider);
    final offlineMode = ref.watch(offlineModeProvider);

    if (isOnline && offlineMode.syncStatus == SyncStatus.synced) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          color: _getIndicatorColor(isOnline, offlineMode.syncStatus),
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Color _getIndicatorColor(bool isOnline, SyncStatus syncStatus) {
    if (!isOnline) return Colors.red;

    switch (syncStatus) {
      case SyncStatus.syncing:
        return Colors.blue;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.failed:
        return Colors.orange;
      case SyncStatus.pending:
        return Colors.amber;
      case SyncStatus.offline:
        return Colors.grey;
    }
  }
}

/// Floating offline status banner
class FloatingOfflineStatusBanner extends ConsumerWidget {
  const FloatingOfflineStatusBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isOnline = ref.watch(connectivityProvider);
    final offlineMode = ref.watch(offlineModeProvider);

    // Only show when offline or sync issues
    if (isOnline && offlineMode.syncStatus == SyncStatus.synced) {
      return const SizedBox.shrink();
    }

    return Positioned(
      top: MediaQuery.of(context).padding.top + 8,
      left: 16,
      right: 16,
      child: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: _getBannerColor(isOnline, offlineMode.syncStatus),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              FaIcon(
                _getBannerIcon(isOnline, offlineMode.syncStatus),
                size: 16,
                color: Colors.white,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _getBannerText(isOnline, offlineMode.syncStatus),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (isOnline && offlineMode.syncStatus == SyncStatus.failed)
                TextButton(
                  onPressed: () {
                    ref.read(offlineModeProvider.notifier).syncOfflineContent();
                  },
                  child: const Text(
                    'Retry',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getBannerColor(bool isOnline, SyncStatus syncStatus) {
    if (!isOnline) return Colors.red;

    switch (syncStatus) {
      case SyncStatus.syncing:
        return Colors.blue;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.failed:
        return Colors.orange;
      case SyncStatus.pending:
        return Colors.amber;
      case SyncStatus.offline:
        return Colors.grey;
    }
  }

  IconData _getBannerIcon(bool isOnline, SyncStatus syncStatus) {
    if (!isOnline) return FontAwesomeIcons.wifiSlash;

    switch (syncStatus) {
      case SyncStatus.syncing:
        return FontAwesomeIcons.arrowsRotate;
      case SyncStatus.synced:
        return FontAwesomeIcons.check;
      case SyncStatus.failed:
        return FontAwesomeIcons.triangleExclamation;
      case SyncStatus.pending:
        return FontAwesomeIcons.clock;
      case SyncStatus.offline:
        return FontAwesomeIcons.download;
    }
  }

  String _getBannerText(bool isOnline, SyncStatus syncStatus) {
    if (!isOnline) return 'You\'re offline. Some features may be limited.';

    switch (syncStatus) {
      case SyncStatus.syncing:
        return 'Syncing your data...';
      case SyncStatus.synced:
        return 'All data is up to date';
      case SyncStatus.failed:
        return 'Sync failed. Tap to retry.';
      case SyncStatus.pending:
        return 'Sync pending...';
      case SyncStatus.offline:
        return 'Offline mode active';
    }
  }
}
