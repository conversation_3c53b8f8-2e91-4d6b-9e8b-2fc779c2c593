import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:culture_connect/models/offline/bandwidth_usage.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/connectivity_service.dart';

/// Bandwidth usage controller for optimizing offline sync operations
class BandwidthUsageController {
  static final BandwidthUsageController _instance = BandwidthUsageController._internal();
  factory BandwidthUsageController() => _instance;
  BandwidthUsageController._internal();

  final LoggingService _loggingService = LoggingService();
  final ConnectivityService _connectivityService = ConnectivityService();
  
  late SharedPreferences _prefs;
  
  // Bandwidth configuration
  static const String _bandwidthStatsKey = 'bandwidth_usage_stats';
  static const String _bandwidthLimitsKey = 'bandwidth_limits';
  static const String _bandwidthPolicyKey = 'bandwidth_policy';
  
  // Default limits (bytes per day)
  static const int _defaultWiFiDailyLimit = 1024 * 1024 * 1024; // 1GB
  static const int _defaultMobileDailyLimit = 100 * 1024 * 1024; // 100MB
  static const int _defaultRoamingDailyLimit = 10 * 1024 * 1024; // 10MB
  
  // Bandwidth tracking
  final Map<String, BandwidthSession> _activeSessions = {};
  final StreamController<BandwidthEvent> _bandwidthEventController = 
      StreamController<BandwidthEvent>.broadcast();
  
  Timer? _trackingTimer;
  bool _isInitialized = false;
  BandwidthPolicy _currentPolicy = BandwidthPolicy.balanced;

  /// Initialize the bandwidth usage controller
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadBandwidthPolicy();
      await _startBandwidthTracking();
      
      _isInitialized = true;
      _loggingService.info('BandwidthUsageController initialized');
    } catch (e) {
      _loggingService.error('Error initializing BandwidthUsageController: $e');
      rethrow;
    }
  }

  /// Load bandwidth policy from preferences
  Future<void> _loadBandwidthPolicy() async {
    final policyIndex = _prefs.getInt(_bandwidthPolicyKey) ?? BandwidthPolicy.balanced.index;
    _currentPolicy = BandwidthPolicy.values[policyIndex];
  }

  /// Start bandwidth tracking
  Future<void> _startBandwidthTracking() async {
    _trackingTimer?.cancel();
    _trackingTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      _updateBandwidthStats();
    });
  }

  /// Update bandwidth statistics
  void _updateBandwidthStats() {
    // Update active sessions
    final now = DateTime.now();
    _activeSessions.removeWhere((key, session) {
      final isExpired = now.difference(session.startTime).inMinutes > 30;
      if (isExpired) {
        _emitBandwidthEvent(BandwidthEventType.sessionEnded, sessionId: key);
      }
      return isExpired;
    });
  }

  /// Start a new bandwidth session
  String startBandwidthSession({
    required String operationType,
    int? estimatedBytes,
  }) {
    final sessionId = _generateSessionId();
    final session = BandwidthSession(
      id: sessionId,
      operationType: operationType,
      startTime: DateTime.now(),
      estimatedBytes: estimatedBytes ?? 0,
      actualBytes: 0,
      connectionType: _getCurrentConnectionType(),
    );
    
    _activeSessions[sessionId] = session;
    _emitBandwidthEvent(BandwidthEventType.sessionStarted, sessionId: sessionId);
    
    return sessionId;
  }

  /// Update bandwidth usage for a session
  void updateBandwidthUsage(String sessionId, int bytesUsed) {
    final session = _activeSessions[sessionId];
    if (session == null) return;
    
    final updatedSession = session.copyWith(
      actualBytes: session.actualBytes + bytesUsed,
    );
    
    _activeSessions[sessionId] = updatedSession;
    _emitBandwidthEvent(BandwidthEventType.usageUpdated, 
        sessionId: sessionId, bytesUsed: bytesUsed);
  }

  /// End a bandwidth session
  Future<void> endBandwidthSession(String sessionId) async {
    final session = _activeSessions.remove(sessionId);
    if (session == null) return;
    
    // Save session to persistent storage
    await _saveBandwidthSession(session);
    _emitBandwidthEvent(BandwidthEventType.sessionEnded, sessionId: sessionId);
  }

  /// Check if operation is allowed based on bandwidth limits
  Future<BandwidthDecision> checkBandwidthAllowance({
    required String operationType,
    required int estimatedBytes,
  }) async {
    final currentUsage = await getDailyBandwidthUsage();
    final limits = await getBandwidthLimits();
    final connectionType = _getCurrentConnectionType();
    
    // Get appropriate limit based on connection type
    int dailyLimit;
    switch (connectionType) {
      case ConnectionType.wifi:
        dailyLimit = limits.wifiDailyLimit;
        break;
      case ConnectionType.mobile:
        dailyLimit = limits.mobileDailyLimit;
        break;
      case ConnectionType.roaming:
        dailyLimit = limits.roamingDailyLimit;
        break;
      case ConnectionType.none:
        return BandwidthDecision(
          allowed: false,
          reason: 'No network connection available',
          recommendedAction: BandwidthAction.waitForConnection,
        );
    }
    
    final currentUsageForType = _getCurrentUsageForConnectionType(currentUsage, connectionType);
    final projectedUsage = currentUsageForType + estimatedBytes;
    
    // Apply policy-based decisions
    return _makeBandwidthDecision(
      operationType: operationType,
      estimatedBytes: estimatedBytes,
      currentUsage: currentUsageForType,
      dailyLimit: dailyLimit,
      projectedUsage: projectedUsage,
      connectionType: connectionType,
    );
  }

  /// Make bandwidth decision based on policy
  BandwidthDecision _makeBandwidthDecision({
    required String operationType,
    required int estimatedBytes,
    required int currentUsage,
    required int dailyLimit,
    required int projectedUsage,
    required ConnectionType connectionType,
  }) {
    final usagePercent = (currentUsage / dailyLimit * 100).round();
    final projectedPercent = (projectedUsage / dailyLimit * 100).round();
    
    // Always allow critical operations
    if (_isCriticalOperation(operationType)) {
      return BandwidthDecision(
        allowed: true,
        reason: 'Critical operation allowed',
        recommendedAction: BandwidthAction.proceed,
        priority: BandwidthPriority.critical,
      );
    }
    
    // Check if we're over the limit
    if (projectedPercent > 100) {
      return BandwidthDecision(
        allowed: false,
        reason: 'Daily bandwidth limit would be exceeded',
        recommendedAction: _getRecommendedAction(connectionType, usagePercent),
        estimatedWaitTime: _calculateWaitTime(),
      );
    }
    
    // Apply policy-specific logic
    switch (_currentPolicy) {
      case BandwidthPolicy.conservative:
        return _makeConservativeDecision(usagePercent, projectedPercent, operationType);
      case BandwidthPolicy.balanced:
        return _makeBalancedDecision(usagePercent, projectedPercent, operationType);
      case BandwidthPolicy.aggressive:
        return _makeAggressiveDecision(usagePercent, projectedPercent, operationType);
    }
  }

  /// Make conservative bandwidth decision
  BandwidthDecision _makeConservativeDecision(int usagePercent, int projectedPercent, String operationType) {
    if (projectedPercent > 70) {
      return BandwidthDecision(
        allowed: false,
        reason: 'Conservative policy: preserving bandwidth',
        recommendedAction: BandwidthAction.waitForWifi,
        priority: BandwidthPriority.low,
      );
    }
    
    return BandwidthDecision(
      allowed: true,
      reason: 'Conservative policy: operation allowed',
      recommendedAction: BandwidthAction.proceed,
      priority: _getOperationPriority(operationType),
    );
  }

  /// Make balanced bandwidth decision
  BandwidthDecision _makeBalancedDecision(int usagePercent, int projectedPercent, String operationType) {
    if (projectedPercent > 85) {
      return BandwidthDecision(
        allowed: false,
        reason: 'Balanced policy: approaching limit',
        recommendedAction: BandwidthAction.defer,
        priority: BandwidthPriority.normal,
      );
    }
    
    if (projectedPercent > 70 && !_isHighPriorityOperation(operationType)) {
      return BandwidthDecision(
        allowed: false,
        reason: 'Balanced policy: deferring low priority operation',
        recommendedAction: BandwidthAction.waitForWifi,
        priority: BandwidthPriority.low,
      );
    }
    
    return BandwidthDecision(
      allowed: true,
      reason: 'Balanced policy: operation allowed',
      recommendedAction: BandwidthAction.proceed,
      priority: _getOperationPriority(operationType),
    );
  }

  /// Make aggressive bandwidth decision
  BandwidthDecision _makeAggressiveDecision(int usagePercent, int projectedPercent, String operationType) {
    if (projectedPercent > 95) {
      return BandwidthDecision(
        allowed: false,
        reason: 'Aggressive policy: near limit',
        recommendedAction: BandwidthAction.defer,
        priority: BandwidthPriority.normal,
      );
    }
    
    return BandwidthDecision(
      allowed: true,
      reason: 'Aggressive policy: operation allowed',
      recommendedAction: BandwidthAction.proceed,
      priority: _getOperationPriority(operationType),
    );
  }

  /// Get daily bandwidth usage
  Future<DailyBandwidthUsage> getDailyBandwidthUsage() async {
    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';
    
    final statsJson = _prefs.getString('$_bandwidthStatsKey-$todayKey');
    if (statsJson == null) {
      return DailyBandwidthUsage(
        date: today,
        wifiUsage: 0,
        mobileUsage: 0,
        roamingUsage: 0,
        totalUsage: 0,
      );
    }
    
    return DailyBandwidthUsage.fromJson(jsonDecode(statsJson));
  }

  /// Get bandwidth limits
  Future<BandwidthLimits> getBandwidthLimits() async {
    final limitsJson = _prefs.getString(_bandwidthLimitsKey);
    if (limitsJson == null) {
      return BandwidthLimits(
        wifiDailyLimit: _defaultWiFiDailyLimit,
        mobileDailyLimit: _defaultMobileDailyLimit,
        roamingDailyLimit: _defaultRoamingDailyLimit,
      );
    }
    
    return BandwidthLimits.fromJson(jsonDecode(limitsJson));
  }

  /// Set bandwidth limits
  Future<void> setBandwidthLimits(BandwidthLimits limits) async {
    await _prefs.setString(_bandwidthLimitsKey, jsonEncode(limits.toJson()));
    _loggingService.info('Bandwidth limits updated');
  }

  /// Set bandwidth policy
  Future<void> setBandwidthPolicy(BandwidthPolicy policy) async {
    _currentPolicy = policy;
    await _prefs.setInt(_bandwidthPolicyKey, policy.index);
    _loggingService.info('Bandwidth policy set to: $policy');
  }

  /// Get current bandwidth policy
  BandwidthPolicy get currentPolicy => _currentPolicy;

  /// Get bandwidth usage statistics
  Future<List<DailyBandwidthUsage>> getBandwidthHistory({int days = 30}) async {
    final history = <DailyBandwidthUsage>[];
    final now = DateTime.now();
    
    for (int i = 0; i < days; i++) {
      final date = now.subtract(Duration(days: i));
      final dateKey = '${date.year}-${date.month}-${date.day}';
      
      final statsJson = _prefs.getString('$_bandwidthStatsKey-$dateKey');
      if (statsJson != null) {
        history.add(DailyBandwidthUsage.fromJson(jsonDecode(statsJson)));
      }
    }
    
    return history.reversed.toList();
  }

  /// Stream of bandwidth events
  Stream<BandwidthEvent> get bandwidthEventStream => _bandwidthEventController.stream;

  /// Helper methods
  String _generateSessionId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  ConnectionType _getCurrentConnectionType() {
    // This would be implemented based on actual connectivity service
    return ConnectionType.wifi; // Placeholder
  }

  int _getCurrentUsageForConnectionType(DailyBandwidthUsage usage, ConnectionType type) {
    switch (type) {
      case ConnectionType.wifi:
        return usage.wifiUsage;
      case ConnectionType.mobile:
        return usage.mobileUsage;
      case ConnectionType.roaming:
        return usage.roamingUsage;
      case ConnectionType.none:
        return 0;
    }
  }

  bool _isCriticalOperation(String operationType) {
    const criticalOperations = ['emergency_sync', 'security_update', 'critical_message'];
    return criticalOperations.contains(operationType);
  }

  bool _isHighPriorityOperation(String operationType) {
    const highPriorityOperations = ['user_data_sync', 'message_sync', 'booking_sync'];
    return highPriorityOperations.contains(operationType);
  }

  BandwidthPriority _getOperationPriority(String operationType) {
    if (_isCriticalOperation(operationType)) return BandwidthPriority.critical;
    if (_isHighPriorityOperation(operationType)) return BandwidthPriority.high;
    return BandwidthPriority.normal;
  }

  BandwidthAction _getRecommendedAction(ConnectionType connectionType, int usagePercent) {
    if (connectionType == ConnectionType.wifi) {
      return BandwidthAction.defer;
    } else if (usagePercent > 90) {
      return BandwidthAction.waitForWifi;
    } else {
      return BandwidthAction.defer;
    }
  }

  Duration _calculateWaitTime() {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    return tomorrow.difference(now);
  }

  Future<void> _saveBandwidthSession(BandwidthSession session) async {
    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';
    
    final currentUsage = await getDailyBandwidthUsage();
    final updatedUsage = _updateUsageWithSession(currentUsage, session);
    
    await _prefs.setString('$_bandwidthStatsKey-$todayKey', jsonEncode(updatedUsage.toJson()));
  }

  DailyBandwidthUsage _updateUsageWithSession(DailyBandwidthUsage currentUsage, BandwidthSession session) {
    switch (session.connectionType) {
      case ConnectionType.wifi:
        return currentUsage.copyWith(
          wifiUsage: currentUsage.wifiUsage + session.actualBytes,
          totalUsage: currentUsage.totalUsage + session.actualBytes,
        );
      case ConnectionType.mobile:
        return currentUsage.copyWith(
          mobileUsage: currentUsage.mobileUsage + session.actualBytes,
          totalUsage: currentUsage.totalUsage + session.actualBytes,
        );
      case ConnectionType.roaming:
        return currentUsage.copyWith(
          roamingUsage: currentUsage.roamingUsage + session.actualBytes,
          totalUsage: currentUsage.totalUsage + session.actualBytes,
        );
      case ConnectionType.none:
        return currentUsage;
    }
  }

  void _emitBandwidthEvent(BandwidthEventType type, {String? sessionId, int? bytesUsed}) {
    _bandwidthEventController.add(BandwidthEvent(
      type: type,
      timestamp: DateTime.now(),
      sessionId: sessionId,
      bytesUsed: bytesUsed,
    ));
  }

  /// Dispose resources
  void dispose() {
    _trackingTimer?.cancel();
    _bandwidthEventController.close();
  }
}

/// Bandwidth session for tracking usage
class BandwidthSession {
  final String id;
  final String operationType;
  final DateTime startTime;
  final int estimatedBytes;
  final int actualBytes;
  final ConnectionType connectionType;

  const BandwidthSession({
    required this.id,
    required this.operationType,
    required this.startTime,
    required this.estimatedBytes,
    required this.actualBytes,
    required this.connectionType,
  });

  BandwidthSession copyWith({
    String? id,
    String? operationType,
    DateTime? startTime,
    int? estimatedBytes,
    int? actualBytes,
    ConnectionType? connectionType,
  }) {
    return BandwidthSession(
      id: id ?? this.id,
      operationType: operationType ?? this.operationType,
      startTime: startTime ?? this.startTime,
      estimatedBytes: estimatedBytes ?? this.estimatedBytes,
      actualBytes: actualBytes ?? this.actualBytes,
      connectionType: connectionType ?? this.connectionType,
    );
  }
}

/// Bandwidth decision result
class BandwidthDecision {
  final bool allowed;
  final String reason;
  final BandwidthAction recommendedAction;
  final BandwidthPriority priority;
  final Duration? estimatedWaitTime;

  const BandwidthDecision({
    required this.allowed,
    required this.reason,
    required this.recommendedAction,
    this.priority = BandwidthPriority.normal,
    this.estimatedWaitTime,
  });
}

/// Daily bandwidth usage
class DailyBandwidthUsage {
  final DateTime date;
  final int wifiUsage;
  final int mobileUsage;
  final int roamingUsage;
  final int totalUsage;

  const DailyBandwidthUsage({
    required this.date,
    required this.wifiUsage,
    required this.mobileUsage,
    required this.roamingUsage,
    required this.totalUsage,
  });

  DailyBandwidthUsage copyWith({
    DateTime? date,
    int? wifiUsage,
    int? mobileUsage,
    int? roamingUsage,
    int? totalUsage,
  }) {
    return DailyBandwidthUsage(
      date: date ?? this.date,
      wifiUsage: wifiUsage ?? this.wifiUsage,
      mobileUsage: mobileUsage ?? this.mobileUsage,
      roamingUsage: roamingUsage ?? this.roamingUsage,
      totalUsage: totalUsage ?? this.totalUsage,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'wifiUsage': wifiUsage,
      'mobileUsage': mobileUsage,
      'roamingUsage': roamingUsage,
      'totalUsage': totalUsage,
    };
  }

  factory DailyBandwidthUsage.fromJson(Map<String, dynamic> json) {
    return DailyBandwidthUsage(
      date: DateTime.parse(json['date'] as String),
      wifiUsage: json['wifiUsage'] as int,
      mobileUsage: json['mobileUsage'] as int,
      roamingUsage: json['roamingUsage'] as int,
      totalUsage: json['totalUsage'] as int,
    );
  }
}

/// Bandwidth limits configuration
class BandwidthLimits {
  final int wifiDailyLimit;
  final int mobileDailyLimit;
  final int roamingDailyLimit;

  const BandwidthLimits({
    required this.wifiDailyLimit,
    required this.mobileDailyLimit,
    required this.roamingDailyLimit,
  });

  Map<String, dynamic> toJson() {
    return {
      'wifiDailyLimit': wifiDailyLimit,
      'mobileDailyLimit': mobileDailyLimit,
      'roamingDailyLimit': roamingDailyLimit,
    };
  }

  factory BandwidthLimits.fromJson(Map<String, dynamic> json) {
    return BandwidthLimits(
      wifiDailyLimit: json['wifiDailyLimit'] as int,
      mobileDailyLimit: json['mobileDailyLimit'] as int,
      roamingDailyLimit: json['roamingDailyLimit'] as int,
    );
  }
}

/// Bandwidth event
class BandwidthEvent {
  final BandwidthEventType type;
  final DateTime timestamp;
  final String? sessionId;
  final int? bytesUsed;

  const BandwidthEvent({
    required this.type,
    required this.timestamp,
    this.sessionId,
    this.bytesUsed,
  });
}

/// Bandwidth event types
enum BandwidthEventType {
  sessionStarted,
  sessionEnded,
  usageUpdated,
  limitExceeded,
  policyChanged,
}

/// Connection types
enum ConnectionType {
  wifi,
  mobile,
  roaming,
  none,
}

/// Bandwidth policies
enum BandwidthPolicy {
  conservative,
  balanced,
  aggressive,
}

/// Bandwidth actions
enum BandwidthAction {
  proceed,
  defer,
  waitForWifi,
  waitForConnection,
}

/// Bandwidth priorities
enum BandwidthPriority {
  low,
  normal,
  high,
  critical,
}
