import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/services/enhanced_offline_cache_service.dart';
import 'package:culture_connect/models/offline/content_priority.dart';

/// Provider for the enhanced offline cache service
final enhancedOfflineCacheServiceProvider = Provider<EnhancedOfflineCacheService>((ref) {
  return EnhancedOfflineCacheService();
});

/// Provider for cache statistics
final cacheStatisticsProvider = FutureProvider<Map<String, CacheStatistics>>((ref) async {
  final cacheService = ref.read(enhancedOfflineCacheServiceProvider);
  return cacheService.getCacheStatistics();
});

/// Provider for total cache size
final totalCacheSizeProvider = Provider<int>((ref) {
  final cacheService = ref.read(enhancedOfflineCacheServiceProvider);
  return cacheService.getTotalCacheSize();
});

/// Provider for cache events stream
final cacheEventsProvider = StreamProvider<CacheEvent>((ref) {
  final cacheService = ref.read(enhancedOfflineCacheServiceProvider);
  return cacheService.cacheEventStream;
});

/// State notifier for managing cache operations
class CacheOperationsNotifier extends StateNotifier<CacheOperationsState> {
  final EnhancedOfflineCacheService _cacheService;

  CacheOperationsNotifier(this._cacheService) : super(const CacheOperationsState());

  /// Cache content with intelligent strategy
  Future<bool> cacheContent({
    required String contentId,
    required String contentType,
    required Map<String, dynamic> data,
    ContentPriority priority = ContentPriority.medium,
    Duration? customExpiry,
    List<String>? tags,
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final success = await _cacheService.cacheContent(
        contentId: contentId,
        contentType: contentType,
        data: data,
        priority: priority,
        customExpiry: customExpiry,
        tags: tags,
      );
      
      state = state.copyWith(
        isLoading: false,
        lastOperation: success ? 'Content cached successfully' : 'Failed to cache content',
      );
      
      return success;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Get cached content
  Future<Map<String, dynamic>?> getCachedContent(String contentId) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final data = await _cacheService.getCachedContent(contentId);
      
      state = state.copyWith(
        isLoading: false,
        lastOperation: data != null ? 'Content retrieved from cache' : 'Content not found in cache',
      );
      
      return data;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return null;
    }
  }

  /// Check if content is cached
  bool isCached(String contentId) {
    return _cacheService.isCached(contentId);
  }

  /// Clean up expired content
  Future<int> cleanupExpiredContent() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final cleanedCount = await _cacheService.cleanupExpiredContent();
      
      state = state.copyWith(
        isLoading: false,
        lastOperation: 'Cleaned up $cleanedCount expired items',
      );
      
      return cleanedCount;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return 0;
    }
  }

  /// Perform smart cleanup
  Future<int> performSmartCleanup({int? targetSizeBytes}) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final cleanedCount = await _cacheService.performSmartCleanup(
        targetSizeBytes: targetSizeBytes,
      );
      
      state = state.copyWith(
        isLoading: false,
        lastOperation: 'Smart cleanup: removed $cleanedCount items',
      );
      
      return cleanedCount;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return 0;
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear last operation message
  void clearLastOperation() {
    state = state.copyWith(lastOperation: null);
  }
}

/// Provider for cache operations
final cacheOperationsProvider = StateNotifierProvider<CacheOperationsNotifier, CacheOperationsState>((ref) {
  final cacheService = ref.read(enhancedOfflineCacheServiceProvider);
  return CacheOperationsNotifier(cacheService);
});

/// State class for cache operations
class CacheOperationsState {
  final bool isLoading;
  final String? error;
  final String? lastOperation;

  const CacheOperationsState({
    this.isLoading = false,
    this.error,
    this.lastOperation,
  });

  CacheOperationsState copyWith({
    bool? isLoading,
    String? error,
    String? lastOperation,
  }) {
    return CacheOperationsState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastOperation: lastOperation,
    );
  }
}

/// Provider for cache initialization
final cacheInitializationProvider = FutureProvider<void>((ref) async {
  final cacheService = ref.read(enhancedOfflineCacheServiceProvider);
  await cacheService.initialize();
});

/// Provider for checking if cache is initialized
final cacheInitializedProvider = Provider<bool>((ref) {
  final initAsync = ref.watch(cacheInitializationProvider);
  return initAsync.when(
    data: (_) => true,
    loading: () => false,
    error: (_, __) => false,
  );
});

/// Provider for cache health status
final cacheHealthProvider = Provider<CacheHealthStatus>((ref) {
  final totalSize = ref.watch(totalCacheSizeProvider);
  final maxSize = 500 * 1024 * 1024; // 500 MB
  
  final usagePercentage = (totalSize / maxSize * 100).round();
  
  CacheHealthLevel level;
  String message;
  
  if (usagePercentage < 50) {
    level = CacheHealthLevel.good;
    message = 'Cache is healthy';
  } else if (usagePercentage < 80) {
    level = CacheHealthLevel.warning;
    message = 'Cache usage is moderate';
  } else if (usagePercentage < 95) {
    level = CacheHealthLevel.critical;
    message = 'Cache usage is high';
  } else {
    level = CacheHealthLevel.full;
    message = 'Cache is nearly full';
  }
  
  return CacheHealthStatus(
    level: level,
    message: message,
    usagePercentage: usagePercentage,
    totalSize: totalSize,
    maxSize: maxSize,
  );
});

/// Cache health status
class CacheHealthStatus {
  final CacheHealthLevel level;
  final String message;
  final int usagePercentage;
  final int totalSize;
  final int maxSize;

  const CacheHealthStatus({
    required this.level,
    required this.message,
    required this.usagePercentage,
    required this.totalSize,
    required this.maxSize,
  });
}

/// Cache health levels
enum CacheHealthLevel {
  good,
  warning,
  critical,
  full,
}

/// Provider for cache content types
final cacheContentTypesProvider = Provider<List<String>>((ref) {
  final statistics = ref.watch(cacheStatisticsProvider);
  
  return statistics.when(
    data: (stats) => stats.keys.toList(),
    loading: () => <String>[],
    error: (_, __) => <String>[],
  );
});

/// Provider for cache statistics by content type
final cacheStatisticsByTypeProvider = Provider.family<CacheStatistics?, String>((ref, contentType) {
  final statistics = ref.watch(cacheStatisticsProvider);
  
  return statistics.when(
    data: (stats) => stats[contentType],
    loading: () => null,
    error: (_, __) => null,
  );
});
