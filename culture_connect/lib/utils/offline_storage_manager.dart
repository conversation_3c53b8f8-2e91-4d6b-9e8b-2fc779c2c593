import 'dart:async';
import 'dart:io';
import 'dart:convert';

import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:culture_connect/models/offline/storage_usage.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Comprehensive storage management utility for offline functionality
class OfflineStorageManager {
  static final OfflineStorageManager _instance = OfflineStorageManager._internal();
  factory OfflineStorageManager() => _instance;
  OfflineStorageManager._internal();

  final LoggingService _loggingService = LoggingService();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  late SharedPreferences _prefs;
  late Directory _appDocumentsDir;
  late Directory _appCacheDir;
  late Directory _offlineDataDir;
  
  // Storage configuration
  static const String _storageStatsKey = 'offline_storage_stats';
  static const String _storageQuotaKey = 'offline_storage_quota';
  static const int _defaultQuotaMB = 500; // 500 MB default quota
  static const int _warningThresholdPercent = 80;
  static const int _criticalThresholdPercent = 95;
  
  // Storage categories
  static const Map<String, String> _storageCategories = {
    'cache': 'Cache Data',
    'offline_content': 'Offline Content',
    'user_data': 'User Data',
    'media': 'Media Files',
    'logs': 'Application Logs',
    'temp': 'Temporary Files',
  };
  
  bool _isInitialized = false;

  /// Initialize the storage manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _appDocumentsDir = await getApplicationDocumentsDirectory();
      _appCacheDir = await getTemporaryDirectory();
      
      // Create offline data directory
      _offlineDataDir = Directory('${_appDocumentsDir.path}/offline_data');
      if (!await _offlineDataDir.exists()) {
        await _offlineDataDir.create(recursive: true);
      }
      
      // Create category subdirectories
      for (final category in _storageCategories.keys) {
        final categoryDir = Directory('${_offlineDataDir.path}/$category');
        if (!await categoryDir.exists()) {
          await categoryDir.create(recursive: true);
        }
      }
      
      _isInitialized = true;
      _loggingService.info('OfflineStorageManager initialized');
    } catch (e) {
      _loggingService.error('Error initializing OfflineStorageManager: $e');
      rethrow;
    }
  }

  /// Get comprehensive storage usage information
  Future<StorageUsage> getStorageUsage() async {
    await _ensureInitialized();
    
    final categoryUsages = <String, int>{};
    int totalUsed = 0;
    
    // Calculate usage for each category
    for (final category in _storageCategories.keys) {
      final categoryDir = Directory('${_offlineDataDir.path}/$category');
      final categorySize = await _calculateDirectorySize(categoryDir);
      categoryUsages[category] = categorySize;
      totalUsed += categorySize;
    }
    
    // Add cache directory size
    final cacheSize = await _calculateDirectorySize(_appCacheDir);
    categoryUsages['system_cache'] = cacheSize;
    totalUsed += cacheSize;
    
    // Get device storage info
    final deviceInfo = await _getDeviceStorageInfo();
    final quota = _getStorageQuota();
    
    return StorageUsage(
      totalUsed: totalUsed,
      totalQuota: quota,
      categoryUsages: categoryUsages,
      deviceTotalSpace: deviceInfo['total'] ?? 0,
      deviceFreeSpace: deviceInfo['free'] ?? 0,
      lastUpdated: DateTime.now(),
    );
  }

  /// Get storage quota in bytes
  int _getStorageQuota() {
    final quotaMB = _prefs.getInt(_storageQuotaKey) ?? _defaultQuotaMB;
    return quotaMB * 1024 * 1024; // Convert MB to bytes
  }

  /// Set storage quota in MB
  Future<void> setStorageQuota(int quotaMB) async {
    await _ensureInitialized();
    await _prefs.setInt(_storageQuotaKey, quotaMB);
    _loggingService.info('Storage quota set to ${quotaMB}MB');
  }

  /// Check if storage is approaching limits
  Future<StorageHealthStatus> checkStorageHealth() async {
    final usage = await getStorageUsage();
    final usagePercent = (usage.totalUsed / usage.totalQuota * 100).round();
    
    StorageHealthLevel level;
    String message;
    List<String> recommendations = [];
    
    if (usagePercent < _warningThresholdPercent) {
      level = StorageHealthLevel.healthy;
      message = 'Storage usage is within normal limits';
    } else if (usagePercent < _criticalThresholdPercent) {
      level = StorageHealthLevel.warning;
      message = 'Storage usage is approaching limits';
      recommendations = [
        'Consider cleaning up old cached content',
        'Review offline content priorities',
        'Clear temporary files',
      ];
    } else {
      level = StorageHealthLevel.critical;
      message = 'Storage usage is critically high';
      recommendations = [
        'Immediately clean up cached content',
        'Remove low-priority offline content',
        'Clear all temporary files',
        'Consider increasing storage quota',
      ];
    }
    
    return StorageHealthStatus(
      level: level,
      message: message,
      usagePercent: usagePercent,
      recommendations: recommendations,
      storageUsage: usage,
    );
  }

  /// Clean up storage by category
  Future<StorageCleanupResult> cleanupStorage({
    List<String>? categories,
    bool includeExpired = true,
    bool includeLowPriority = false,
    int? targetSizeBytes,
  }) async {
    await _ensureInitialized();
    
    final categoriesToClean = categories ?? _storageCategories.keys.toList();
    int totalFreed = 0;
    int filesRemoved = 0;
    final cleanupDetails = <String, int>{};
    
    for (final category in categoriesToClean) {
      final categoryDir = Directory('${_offlineDataDir.path}/$category');
      if (!await categoryDir.exists()) continue;
      
      final result = await _cleanupCategory(
        categoryDir,
        category,
        includeExpired: includeExpired,
        includeLowPriority: includeLowPriority,
      );
      
      totalFreed += result.bytesFreed;
      filesRemoved += result.filesRemoved;
      cleanupDetails[category] = result.bytesFreed;
      
      // Check if we've reached target size
      if (targetSizeBytes != null && totalFreed >= targetSizeBytes) {
        break;
      }
    }
    
    // Clean system cache if needed
    if (categoriesToClean.contains('system_cache') || targetSizeBytes != null) {
      final cacheResult = await _cleanupSystemCache();
      totalFreed += cacheResult.bytesFreed;
      filesRemoved += cacheResult.filesRemoved;
      cleanupDetails['system_cache'] = cacheResult.bytesFreed;
    }
    
    _loggingService.info('Storage cleanup completed: ${totalFreed ~/ 1024}KB freed, $filesRemoved files removed');
    
    return StorageCleanupResult(
      totalBytesFreed: totalFreed,
      totalFilesRemoved: filesRemoved,
      categoryDetails: cleanupDetails,
      timestamp: DateTime.now(),
    );
  }

  /// Clean up specific category
  Future<CategoryCleanupResult> _cleanupCategory(
    Directory categoryDir,
    String category, {
    required bool includeExpired,
    required bool includeLowPriority,
  }) async {
    int bytesFreed = 0;
    int filesRemoved = 0;
    
    try {
      await for (final entity in categoryDir.list(recursive: true)) {
        if (entity is File) {
          bool shouldDelete = false;
          
          // Check if file is expired
          if (includeExpired) {
            final lastModified = await entity.lastModified();
            final age = DateTime.now().difference(lastModified);
            
            // Different expiry rules for different categories
            Duration maxAge;
            switch (category) {
              case 'cache':
                maxAge = Duration(days: 7);
                break;
              case 'temp':
                maxAge = Duration(hours: 24);
                break;
              case 'logs':
                maxAge = Duration(days: 30);
                break;
              default:
                maxAge = Duration(days: 30);
            }
            
            if (age > maxAge) {
              shouldDelete = true;
            }
          }
          
          // Check if file is low priority (based on filename patterns)
          if (includeLowPriority && !shouldDelete) {
            final filename = entity.path.split('/').last.toLowerCase();
            if (filename.contains('temp') || 
                filename.contains('backup') || 
                filename.startsWith('.')) {
              shouldDelete = true;
            }
          }
          
          if (shouldDelete) {
            try {
              final fileSize = await entity.length();
              await entity.delete();
              bytesFreed += fileSize;
              filesRemoved++;
            } catch (e) {
              _loggingService.warning('Failed to delete file ${entity.path}: $e');
            }
          }
        }
      }
    } catch (e) {
      _loggingService.error('Error cleaning category $category: $e');
    }
    
    return CategoryCleanupResult(
      bytesFreed: bytesFreed,
      filesRemoved: filesRemoved,
    );
  }

  /// Clean up system cache
  Future<CategoryCleanupResult> _cleanupSystemCache() async {
    int bytesFreed = 0;
    int filesRemoved = 0;
    
    try {
      await for (final entity in _appCacheDir.list(recursive: true)) {
        if (entity is File) {
          try {
            final fileSize = await entity.length();
            await entity.delete();
            bytesFreed += fileSize;
            filesRemoved++;
          } catch (e) {
            _loggingService.warning('Failed to delete cache file ${entity.path}: $e');
          }
        }
      }
    } catch (e) {
      _loggingService.error('Error cleaning system cache: $e');
    }
    
    return CategoryCleanupResult(
      bytesFreed: bytesFreed,
      filesRemoved: filesRemoved,
    );
  }

  /// Get device storage information
  Future<Map<String, int>> _getDeviceStorageInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        // Note: Getting actual storage info requires platform-specific implementation
        return {
          'total': 32 * 1024 * 1024 * 1024, // 32GB placeholder
          'free': 16 * 1024 * 1024 * 1024,  // 16GB placeholder
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        // Note: iOS doesn't provide direct storage access
        return {
          'total': 64 * 1024 * 1024 * 1024, // 64GB placeholder
          'free': 32 * 1024 * 1024 * 1024,  // 32GB placeholder
        };
      }
    } catch (e) {
      _loggingService.warning('Could not get device storage info: $e');
    }
    
    return {
      'total': 0,
      'free': 0,
    };
  }

  /// Calculate directory size recursively
  Future<int> _calculateDirectorySize(Directory directory) async {
    int totalSize = 0;
    
    try {
      if (!await directory.exists()) return 0;
      
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          try {
            totalSize += await entity.length();
          } catch (e) {
            // File might be deleted or inaccessible, continue
          }
        }
      }
    } catch (e) {
      _loggingService.warning('Error calculating directory size for ${directory.path}: $e');
    }
    
    return totalSize;
  }

  /// Get storage directory for category
  Directory getStorageDirectory(String category) {
    if (!_storageCategories.containsKey(category)) {
      throw ArgumentError('Unknown storage category: $category');
    }
    return Directory('${_offlineDataDir.path}/$category');
  }

  /// Get available storage categories
  Map<String, String> get storageCategories => Map.from(_storageCategories);

  /// Ensure the storage manager is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Export storage statistics
  Future<Map<String, dynamic>> exportStorageStats() async {
    final usage = await getStorageUsage();
    final health = await checkStorageHealth();
    
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'storage_usage': usage.toJson(),
      'storage_health': {
        'level': health.level.toString(),
        'message': health.message,
        'usage_percent': health.usagePercent,
        'recommendations': health.recommendations,
      },
      'quota_mb': _getStorageQuota() ~/ (1024 * 1024),
    };
  }

  /// Import storage configuration
  Future<void> importStorageConfig(Map<String, dynamic> config) async {
    await _ensureInitialized();
    
    if (config.containsKey('quota_mb')) {
      await setStorageQuota(config['quota_mb'] as int);
    }
    
    _loggingService.info('Storage configuration imported');
  }
}

/// Storage health status
class StorageHealthStatus {
  final StorageHealthLevel level;
  final String message;
  final int usagePercent;
  final List<String> recommendations;
  final StorageUsage storageUsage;

  const StorageHealthStatus({
    required this.level,
    required this.message,
    required this.usagePercent,
    required this.recommendations,
    required this.storageUsage,
  });
}

/// Storage health levels
enum StorageHealthLevel {
  healthy,
  warning,
  critical,
}

/// Storage cleanup result
class StorageCleanupResult {
  final int totalBytesFreed;
  final int totalFilesRemoved;
  final Map<String, int> categoryDetails;
  final DateTime timestamp;

  const StorageCleanupResult({
    required this.totalBytesFreed,
    required this.totalFilesRemoved,
    required this.categoryDetails,
    required this.timestamp,
  });
}

/// Category cleanup result
class CategoryCleanupResult {
  final int bytesFreed;
  final int filesRemoved;

  const CategoryCleanupResult({
    required this.bytesFreed,
    required this.filesRemoved,
  });
}
